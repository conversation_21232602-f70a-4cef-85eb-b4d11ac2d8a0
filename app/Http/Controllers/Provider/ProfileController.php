<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use App\Http\Requests\Provider\ProviderProfileUpdateRequest;
use App\Services\ServiceProviderService;
use App\Services\CityService;
use App\Services\AreaService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

/**
 * Provider Profile Controller
 *
 * Handles provider profile management including basic info,
 * contact information, business details, and media uploads.
 */
class ProfileController extends Controller
{
    public function __construct(
        private ServiceProviderService $serviceProviderService,
        private CityService $cityService,
        private AreaService $areaService
    ) {
    }
    /**
     * Display the provider profile overview.
     */
    public function index(): View
    {
        $provider = Auth::guard('provider')->user();

        return view('provider.profile.index', compact('provider'));
    }

    /**
     * Show the basic information form.
     */
    public function basicInfo(): View
    {
        $provider = Auth::guard('provider')->user();
        $cities = $this->cityService->getAllCities();
        $areas = $provider->city_id ? $this->areaService->getAreasByCity($provider->city_id) : collect();

        return view('provider.profile.basic-info', compact('provider', 'cities', 'areas'));
    }

    /**
     * Update basic information.
     */
    public function updateBasicInfo(ProviderProfileUpdateRequest $request): RedirectResponse
    {
        $provider = Auth::guard('provider')->user();

        try {
            // Update provider profile with translations and logo
            $this->serviceProviderService->updateProfile(
                $provider->id,
                $request->validated(),
                $request->file('logo')
            );

            return back()->with('success', __('provider/profile.basic_info.messages.updated_successfully'));
        } catch (\Exception $e) {
            return back()
                ->with('error', __('provider/profile.basic_info.messages.update_failed'))
                ->withInput();
        }
    }

    /**
     * Show the contact information form.
     */
    public function contactInfo(): View
    {
        $provider = Auth::guard('provider')->user();

        return view('provider.profile.contact-info', compact('provider'));
    }

    /**
     * Update contact information.
     */
    public function updateContactInfo(Request $request): RedirectResponse
    {
        $provider = Auth::guard('provider')->user();

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:service_providers,email,' . $provider->id,
            'phone' => 'required|string|regex:/^05[0-9]{8}$/',
            'whatsapp' => 'nullable|string|regex:/^05[0-9]{8}$/',
            'address' => 'nullable|string|max:500',
            'city_id' => 'required|exists:cities,id',
            'area_id' => 'required|exists:areas,id',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update provider contact information
        $provider->update($request->only([
            'email',
            'phone',
            'whatsapp',
            'address',
            'city_id',
            'area_id'
        ]));

        return back()->with('success', __('provider/profile.contact_info_updated'));
    }

    /**
     * Show the business information form.
     */
    public function businessInfo(): View
    {
        $provider = Auth::guard('provider')->user();

        return view('provider.profile.business-info', compact('provider'));
    }

    /**
     * Update business information.
     */
    public function updateBusinessInfo(Request $request): RedirectResponse
    {
        $provider = Auth::guard('provider')->user();

        $validator = Validator::make($request->all(), [
            'business_name' => 'nullable|string|max:255',
            'business_license' => 'nullable|string|max:255',
            'tax_number' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:255',
            'iban' => 'nullable|string|max:34',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update provider business information
        $provider->update($request->only([
            'business_name',
            'business_license',
            'tax_number',
            'bank_account',
            'iban'
        ]));

        return back()->with('success', __('provider/profile.business_info_updated'));
    }

    /**
     * Upload provider logo.
     */
    public function uploadLogo(Request $request): RedirectResponse
    {
        $provider = Auth::guard('provider')->user();

        $validator = Validator::make($request->all(), [
            'logo' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator);
        }

        try {
            // Delete old logo if exists
            if ($provider->logo && Storage::disk('public')->exists($provider->logo)) {
                Storage::disk('public')->delete($provider->logo);
            }

            // Store new logo
            $logoPath = $request->file('logo')->store('providers/logos', 'public');

            // Update provider logo path
            $provider->update(['logo' => $logoPath]);

            return back()->with('success', __('provider/profile.logo_uploaded'));
        } catch (\Exception $e) {
            return back()->with('error', __('provider/profile.logo_upload_failed'));
        }
    }

    /**
     * Upload gallery images.
     */
    public function uploadGallery(Request $request): RedirectResponse
    {
        $provider = Auth::guard('provider')->user();

        $validator = Validator::make($request->all(), [
            'gallery.*' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator);
        }

        try {
            $uploadedImages = [];

            foreach ($request->file('gallery') as $image) {
                $imagePath = $image->store('providers/gallery', 'public');
                $uploadedImages[] = $imagePath;
            }

            // Here you would save the gallery images to a related model
            // For now, we'll just return success

            return back()->with('success', __('provider/profile.gallery_uploaded'));
        } catch (\Exception $e) {
            return back()->with('error', __('provider/profile.gallery_upload_failed'));
        }
    }

    /**
     * Delete gallery image.
     */
    public function deleteGalleryImage(Request $request, int $imageId): RedirectResponse
    {
        try {
            // Here you would delete the image from the gallery model
            // and remove the file from storage

            return back()->with('success', __('provider/profile.gallery_image_deleted'));
        } catch (\Exception $e) {
            return back()->with('error', __('provider/profile.gallery_delete_failed'));
        }
    }

    /**
     * Get areas by city ID for AJAX requests.
     */
    public function getAreasByCity(Request $request): JsonResponse
    {
        $request->validate([
            'city_id' => 'required|integer|exists:cities,id'
        ]);

        try {
            $areas = $this->areaService->getAreasByCity($request->input('city_id'));

            $areasData = $areas->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $areasData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('provider/profile.basic_info.messages.areas_load_failed')
            ], 500);
        }
    }
}
