<?php

namespace App\Http\Requests\Provider;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Provider Profile Update Request
 *
 * Handles validation for provider profile updates including
 * multilingual fields, location data, and image uploads.
 */
class ProviderProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $providerId = auth('provider')->id();

        return [
            // Translatable name fields
            'translations.ar.name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],
            'translations.en.name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],

            // Optional description fields
            'translations.ar.description' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'translations.en.description' => [
                'nullable',
                'string',
                'max:1000'
            ],

            // Email field
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                Rule::unique('service_providers', 'email')->ignore($providerId)
            ],

            // Location fields
            'city_id' => [
                'required',
                'integer',
                'exists:cities,id'
            ],
            'area_id' => [
                'required',
                'integer',
                'exists:areas,id'
            ],

            // Coordinates (optional)
            'latitude' => [
                'nullable',
                'numeric',
                'between:-90,90'
            ],
            'longitude' => [
                'nullable',
                'numeric',
                'between:-180,180'
            ],

            // Logo upload
            'logo' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048', // 2MB max
                'dimensions:min_width=100,min_height=100,max_width=2000,max_height=2000'
            ],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // Name validation messages
            'translations.ar.name.required' => __('provider/profile.validation.name_arabic_required'),
            'translations.en.name.required' => __('provider/profile.validation.name_english_required'),
            'translations.ar.name.min' => __('provider/profile.validation.name_arabic_min'),
            'translations.en.name.min' => __('provider/profile.validation.name_english_min'),
            'translations.ar.name.max' => __('provider/profile.validation.name_arabic_max'),
            'translations.en.name.max' => __('provider/profile.validation.name_english_max'),

            // Description validation messages
            'translations.ar.description.max' => __('provider/profile.validation.description_arabic_max'),
            'translations.en.description.max' => __('provider/profile.validation.description_english_max'),

            // Email validation messages
            'email.required' => __('provider/profile.validation.email_required'),
            'email.email' => __('provider/profile.validation.email_invalid'),
            'email.unique' => __('provider/profile.validation.email_exists'),

            // Location validation messages
            'city_id.required' => __('provider/profile.validation.city_required'),
            'city_id.exists' => __('provider/profile.validation.city_invalid'),
            'area_id.required' => __('provider/profile.validation.area_required'),
            'area_id.exists' => __('provider/profile.validation.area_invalid'),

            // Coordinates validation messages
            'latitude.numeric' => __('provider/profile.validation.latitude_numeric'),
            'latitude.between' => __('provider/profile.validation.latitude_range'),
            'longitude.numeric' => __('provider/profile.validation.longitude_numeric'),
            'longitude.between' => __('provider/profile.validation.longitude_range'),

            // Logo validation messages
            'logo.image' => __('provider/profile.validation.logo_image'),
            'logo.mimes' => __('provider/profile.validation.logo_format'),
            'logo.max' => __('provider/profile.validation.logo_size'),
            'logo.dimensions' => __('provider/profile.validation.logo_dimensions'),
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'translations.ar.name' => __('provider/profile.fields.name_arabic'),
            'translations.en.name' => __('provider/profile.fields.name_english'),
            'translations.ar.description' => __('provider/profile.fields.description_arabic'),
            'translations.en.description' => __('provider/profile.fields.description_english'),
            'email' => __('provider/profile.fields.email'),
            'city_id' => __('provider/profile.fields.city'),
            'area_id' => __('provider/profile.fields.area'),
            'latitude' => __('provider/profile.fields.latitude'),
            'longitude' => __('provider/profile.fields.longitude'),
            'logo' => __('provider/profile.fields.logo'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure coordinates are properly formatted
        if ($this->has('latitude') && $this->input('latitude') !== null) {
            $this->merge(['latitude' => (float) $this->input('latitude')]);
        }

        if ($this->has('longitude') && $this->input('longitude') !== null) {
            $this->merge(['longitude' => (float) $this->input('longitude')]);
        }
    }
}
