<?php

namespace App\Services;

use App\Models\ServiceProvider;
use App\Repositories\Contracts\ServiceProviderRepositoryInterface;
use App\Repositories\Contracts\ProviderServiceRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class ServiceProviderService
{
    public function __construct(
        private ServiceProviderRepositoryInterface $serviceProviderRepository,
        private ProviderServiceRepositoryInterface $providerServiceRepository
    ) {
    }

    /**
     * Create a new service provider.
     */
    public function create(array $data): ServiceProvider
    {
        return $this->serviceProviderRepository->create($data);
    }

    /**
     * Update service provider.
     */
    public function update(int $providerId, array $data): ServiceProvider
    {
        return $this->serviceProviderRepository->update($providerId, $data);
    }

    /**
     * Get service provider with full details.
     */
    public function getWithDetails(int $providerId): ?ServiceProvider
    {
        return $this->serviceProviderRepository->getWithDetails($providerId);
    }

    /**
     * Get service provider with full details for API response.
     */
    public function getProviderWithFullDetails(int $providerId): ?ServiceProvider
    {
        return $this->serviceProviderRepository->getWithFullDetails($providerId);
    }

    /**
     * Search service providers.
     */
    public function search(array $filters): Collection
    {
        $providers = $this->serviceProviderRepository->getActive();

        // Apply filters
        if (!empty($filters['city'])) {
            $providers = $this->serviceProviderRepository->getByCity($filters['city']);
        }

        if (!empty($filters['area'])) {
            $providers = $this->serviceProviderRepository->getByArea($filters['area']);
        }

        if (!empty($filters['min_rating'])) {
            $providers = $this->serviceProviderRepository->getWithMinRating($filters['min_rating']);
        }

        if (!empty($filters['query'])) {
            $providers = $this->serviceProviderRepository->search($filters['query']);
        }

        if (!empty($filters['latitude']) && !empty($filters['longitude'])) {
            $radius = $filters['radius'] ?? 10; // Default 10km radius
            $providers = $this->serviceProviderRepository->getWithinRadius(
                $filters['latitude'],
                $filters['longitude'],
                $radius
            );
        }

        if (!empty($filters['service_category_id'])) {
            $providers = $this->serviceProviderRepository->getByServiceCategory($filters['service_category_id']);
        }

        return $providers;
    }

    /**
     * Get providers near a location.
     */
    public function getNearbyProviders(float $latitude, float $longitude, float $radiusKm = 10): Collection
    {
        return $this->serviceProviderRepository->getWithinRadius($latitude, $longitude, $radiusKm);
    }

    /**
     * Calculate and update provider rating based on service ratings.
     */
    public function updateProviderRating(int $providerId): bool
    {
        $services = $this->providerServiceRepository->getByProvider($providerId);

        if ($services->isEmpty()) {
            return $this->serviceProviderRepository->updateRating($providerId, 0.0);
        }

        $averageRating = $services->avg('rating');
        return $this->serviceProviderRepository->updateRating($providerId, round($averageRating, 2));
    }

    /**
     * Toggle provider active status.
     */
    public function toggleStatus(int $providerId): ServiceProvider
    {
        $provider = $this->serviceProviderRepository->findOrFail($providerId);

        return $this->serviceProviderRepository->update($providerId, [
            'is_active' => !$provider->is_active
        ]);
    }

    /**
     * Get all active service providers.
     */
    public function getActiveProviders(): Collection
    {
        return $this->serviceProviderRepository->getActive();
    }

    /**
     * Search active service providers by name.
     */
    public function searchActiveProviders(string $query): Collection
    {
        return $this->serviceProviderRepository->searchActiveProviders($query);
    }

    /**
     * Get provider statistics.
     */
    public function getStatistics(int $providerId): array
    {
        $provider = $this->serviceProviderRepository->getWithDetails($providerId);

        if (!$provider) {
            return [];
        }

        $totalServices = $provider->services->count();
        $activeServices = $provider->services->where('is_active', true)->count();
        $averagePrice = $provider->services->where('is_active', true)->avg('price');
        $totalFavourites = $provider->services->sum(function ($service) {
            return $service->favourites->count();
        });

        return [
            'total_services' => $totalServices,
            'active_services' => $activeServices,
            'average_price' => round($averagePrice, 2),
            'total_favourites' => $totalFavourites,
            'rating' => $provider->rating,
        ];
    }

    /**
     * Update provider location.
     */
    public function updateLocation(int $providerId, float $latitude, float $longitude, ?string $city = null, ?string $area = null): ServiceProvider
    {
        $data = [
            'latitude' => $latitude,
            'longitude' => $longitude,
        ];

        if ($city) {
            $data['city'] = $city;
        }

        if ($area) {
            $data['area'] = $area;
        }

        return $this->serviceProviderRepository->update($providerId, $data);
    }

    /**
     * Get providers with filters for admin panel.
     */
    public function getProvidersWithFilters(array $filters, int $perPage = 15)
    {
        return $this->serviceProviderRepository->getWithFilters($filters, $perPage);
    }

    /**
     * Get providers statistics for admin dashboard.
     */
    public function getProvidersStatistics(): array
    {
        return $this->serviceProviderRepository->getStatistics();
    }

    /**
     * Bulk update provider status.
     */
    public function bulkUpdateStatus(array $ids, bool $status): int
    {
        return $this->serviceProviderRepository->bulkUpdateStatus($ids, $status);
    }

    /**
     * Restore soft deleted provider.
     */
    public function restore(int $id): bool
    {
        return $this->serviceProviderRepository->restore($id);
    }

    /**
     * Force delete provider.
     */
    public function forceDelete(int $id): bool
    {
        return $this->serviceProviderRepository->forceDelete($id);
    }

    /**
     * Bulk delete providers.
     */
    public function bulkDelete(array $ids): int
    {
        return $this->serviceProviderRepository->bulkDelete($ids);
    }

    /**
     * Bulk restore providers.
     */
    public function bulkRestore(array $ids): int
    {
        return $this->serviceProviderRepository->bulkRestore($ids);
    }

    /**
     * Create provider with translations.
     */
    public function createWithTranslations(array $data): ServiceProvider
    {
        // Extract translations
        $translations = $data['translations'] ?? [];
        unset($data['translations']);

        // Create the provider
        $provider = $this->serviceProviderRepository->create($data);

        // Add translations
        foreach ($translations as $locale => $translation) {
            $provider->translateOrNew($locale)->name = $translation['name'];
            $provider->translateOrNew($locale)->description = $translation['description'] ?? null;
        }

        $provider->save();

        return $provider;
    }

    /**
     * Update provider with translations.
     */
    public function updateWithTranslations(int $providerId, array $data): ServiceProvider
    {
        // Extract translations
        $translations = $data['translations'] ?? [];
        unset($data['translations']);

        // Update the provider
        $provider = $this->serviceProviderRepository->update($providerId, $data);

        // Update translations
        foreach ($translations as $locale => $translation) {
            $provider->translateOrNew($locale)->name = $translation['name'];
            $provider->translateOrNew($locale)->description = $translation['description'] ?? null;
        }

        $provider->save();

        return $provider;
    }

    /**
     * Update provider profile with translations and file uploads.
     */
    public function updateProfile(int $providerId, array $data, $logoFile = null): ServiceProvider
    {
        // Handle logo upload if provided
        if ($logoFile) {
            $data['logo'] = $this->handleLogoUpload($providerId, $logoFile);
        }

        // Extract translations
        $translations = $data['translations'] ?? [];
        unset($data['translations']);

        // Update the provider
        $provider = $this->serviceProviderRepository->update($providerId, $data);

        // Update translations
        foreach ($translations as $locale => $translation) {
            $provider->translateOrNew($locale)->name = $translation['name'];
            $provider->translateOrNew($locale)->description = $translation['description'] ?? null;
        }

        $provider->save();

        return $provider;
    }

    /**
     * Handle logo upload for provider.
     */
    private function handleLogoUpload(int $providerId, $logoFile): string
    {
        // Get current provider to delete old logo
        $provider = $this->serviceProviderRepository->findOrFail($providerId);

        // Delete old logo if exists
        if ($provider->logo && \Storage::disk('public')->exists($provider->logo)) {
            \Storage::disk('public')->delete($provider->logo);
        }

        // Store new logo
        return $logoFile->store('providers/logos', 'public');
    }
}
