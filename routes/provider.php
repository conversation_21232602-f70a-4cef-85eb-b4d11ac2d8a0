<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Provider\DashboardController;
use App\Http\Controllers\Provider\AuthController;
use App\Http\Controllers\Provider\ProfileController;
use App\Http\Controllers\Provider\ServiceController;
use App\Http\Controllers\Provider\BookingController;
use App\Http\Controllers\Provider\WorkingHourController;
use App\Http\Controllers\Provider\SettingController;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

/*
|--------------------------------------------------------------------------
| Provider Dashboard Routes
|--------------------------------------------------------------------------
|
| These routes are specifically for the provider dashboard functionality.
| They are configured to work with the subdomain 'provider.bose.local'
| and include proper localization and authentication middleware.
|
*/

// Provider Subdomain Routes Group
Route::domain('provider.bose.local')->group(function () {

    // Localized Routes Group for Provider Dashboard
    Route::group([
        'prefix' => LaravelLocalization::setLocale(),
        'middleware' => ['localeSessionRedirect', 'localizationRedirect']
    ], function () {

        // Provider Authentication Routes (not protected)
        Route::name('provider.')->group(function () {
            Route::get('/', [AuthController::class, 'showLoginForm'])->name('home');
            Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
            Route::post('/login', [AuthController::class, 'login'])->name('login.submit');
            Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

            // Registration routes
            Route::get('/signup', [AuthController::class, 'showRegistrationForm'])->name('signup');
            Route::post('/register', [AuthController::class, 'register'])->name('register');

            // Email verification routes
            Route::get('/email/verify/{email}', [AuthController::class, 'verifyEmail'])->name('verification.verify');
            Route::post('/email/resend', [AuthController::class, 'resendVerification'])->name('verification.resend');
        });

        // Protected Provider Routes (localized)
        Route::name('provider.')->middleware(['provider.auth'])->group(function () {

            // Dashboard Routes
            Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

            // Dashboard API endpoints for charts and statistics
            Route::prefix('api')->name('api.')->group(function () {
                Route::get('/statistics', [DashboardController::class, 'getStatistics'])->name('statistics');
                Route::get('/recent-bookings', [DashboardController::class, 'getRecentBookings'])->name('recent-bookings');
                Route::get('/earnings-chart', [DashboardController::class, 'getEarningsChart'])->name('earnings-chart');
                Route::get('/service-performance', [DashboardController::class, 'getServicePerformance'])->name('service-performance');
            });

            // Profile Management Routes
            Route::prefix('profile')->name('profile.')->group(function () {
                Route::get('/', [ProfileController::class, 'index'])->name('index');
                Route::get('/basic-info', [ProfileController::class, 'basicInfo'])->name('basic-info');
                Route::put('/basic-info', [ProfileController::class, 'updateBasicInfo'])->name('basic-info.update');
                Route::get('/contact-info', [ProfileController::class, 'contactInfo'])->name('contact-info');
                Route::put('/contact-info', [ProfileController::class, 'updateContactInfo'])->name('contact-info.update');
                Route::get('/business-info', [ProfileController::class, 'businessInfo'])->name('business-info');
                Route::put('/business-info', [ProfileController::class, 'updateBusinessInfo'])->name('business-info.update');
                Route::post('/upload-logo', [ProfileController::class, 'uploadLogo'])->name('upload-logo');
                Route::post('/upload-gallery', [ProfileController::class, 'uploadGallery'])->name('upload-gallery');
                Route::delete('/gallery/{image}', [ProfileController::class, 'deleteGalleryImage'])->name('gallery.delete');

                // AJAX endpoints
                Route::get('/areas-by-city', [ProfileController::class, 'getAreasByCity'])->name('areas-by-city');
            });

            // Working Hours Management Routes
            Route::prefix('working-hours')->name('working-hours.')->group(function () {
                Route::get('/', [WorkingHourController::class, 'index'])->name('index');
                Route::put('/', [WorkingHourController::class, 'update'])->name('update');
                Route::post('/bulk-update', [WorkingHourController::class, 'bulkUpdate'])->name('bulk-update');
            });

            // Services Management Routes
            Route::prefix('services')->name('services.')->group(function () {
                Route::get('/', [ServiceController::class, 'index'])->name('index');
                Route::get('/create', [ServiceController::class, 'create'])->name('create');
                Route::post('/', [ServiceController::class, 'store'])->name('store');
                Route::get('/{service}', [ServiceController::class, 'show'])->name('show');
                Route::get('/{service}/edit', [ServiceController::class, 'edit'])->name('edit');
                Route::put('/{service}', [ServiceController::class, 'update'])->name('update');
                Route::delete('/{service}', [ServiceController::class, 'destroy'])->name('destroy');
                Route::patch('/{service}/toggle-status', [ServiceController::class, 'toggleStatus'])->name('toggle-status');
                Route::post('/bulk-update-status', [ServiceController::class, 'bulkUpdateStatus'])->name('bulk-update-status');
                Route::delete('/bulk-delete', [ServiceController::class, 'bulkDelete'])->name('bulk-delete');
            });

            // Bookings Management Routes
            Route::prefix('bookings')->name('bookings.')->group(function () {
                Route::get('/', [BookingController::class, 'index'])->name('index');
                Route::get('/{booking}', [BookingController::class, 'show'])->name('show');
                Route::patch('/{booking}/accept', [BookingController::class, 'accept'])->name('accept');
                Route::patch('/{booking}/reject', [BookingController::class, 'reject'])->name('reject');
                Route::patch('/{booking}/complete', [BookingController::class, 'complete'])->name('complete');
                Route::patch('/{booking}/cancel', [BookingController::class, 'cancel'])->name('cancel');
                Route::post('/{booking}/notes', [BookingController::class, 'addNote'])->name('add-note');
                Route::get('/calendar/view', [BookingController::class, 'calendar'])->name('calendar');
                Route::get('/api/calendar-events', [BookingController::class, 'getCalendarEvents'])->name('api.calendar-events');
            });

            // Settings Routes
            Route::prefix('settings')->name('settings.')->group(function () {
                Route::get('/', [SettingController::class, 'index'])->name('index');
                Route::get('/account', [SettingController::class, 'account'])->name('account');
                Route::put('/account', [SettingController::class, 'updateAccount'])->name('account.update');
                Route::put('/privacy', [SettingController::class, 'updatePrivacy'])->name('privacy.update');
                Route::put('/preferences', [SettingController::class, 'updatePreferences'])->name('preferences.update');
                Route::get('/notifications', [SettingController::class, 'notifications'])->name('notifications');
                Route::put('/notifications', [SettingController::class, 'updateNotifications'])->name('notifications.update');
                Route::get('/security', [SettingController::class, 'security'])->name('security');
                Route::put('/password', [SettingController::class, 'updatePassword'])->name('password.update');
                Route::post('/two-factor/enable', [SettingController::class, 'enableTwoFactor'])->name('two-factor.enable');
                Route::delete('/two-factor/disable', [SettingController::class, 'disableTwoFactor'])->name('two-factor.disable');
            });
        });
    });
});
